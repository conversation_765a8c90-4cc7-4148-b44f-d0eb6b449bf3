import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth-config';
import { prisma } from '@/lib/prisma';
import { NodeCategory, NodeTier } from '@/lib/marketplace/types';

// GET /api/marketplace/nodes - Search and list nodes
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Parse query parameters
    const search = searchParams.get('search');
    const category = searchParams.get('category') as NodeCategory;
    const tier = searchParams.get('tier') as NodeTier;
    const verified = searchParams.get('verified') === 'true';
    const featured = searchParams.get('featured') === 'true';
    const sortBy = searchParams.get('sortBy') || 'popularity';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');

    // Build where clause
    const where: any = {
      AND: [
        {
          OR: [
            { approvalStatus: 'approved' }, // Show approved nodes
            { approvalStatus: null }, // Show legacy nodes without approval status (for backward compatibility)
          ]
        }
      ]
    };

    if (search) {
      where.AND.push({
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { tags: { contains: search, mode: 'insensitive' } }
        ]
      });
    }

    if (category) {
      where.AND.push({ category });
    }

    if (tier) {
      where.AND.push({ tier });
    }

    if (verified !== undefined) {
      where.AND.push({ verified });
    }

    if (featured !== undefined) {
      where.AND.push({ featured });
    }

    // Build order by clause
    let orderBy: any = {};
    switch (sortBy) {
      case 'rating':
        orderBy = { rating: 'desc' };
        break;
      case 'newest':
        orderBy = { createdAt: 'desc' };
        break;
      case 'price_low':
        orderBy = { price: 'asc' };
        break;
      case 'price_high':
        orderBy = { price: 'desc' };
        break;
      case 'name':
        orderBy = { name: 'asc' };
        break;
      default: // popularity
        orderBy = { downloads: 'desc' };
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Execute queries
    const [nodes, total] = await Promise.all([
      prisma.nodePlugin.findMany({
        where,
        orderBy,
        skip,
        take: limit,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true
            }
          },
          _count: {
            select: {
              reviews: true,
              purchases: true
            }
          }
        }
      }),
      prisma.nodePlugin.count({ where })
    ]);

    // Transform data
    const transformedNodes = nodes.map(node => ({
      id: node.id,
      name: node.name,
      version: node.version,
      description: node.description,
      longDescription: node.longDescription,
      author: {
        name: node.author.name || 'Unknown',
        email: node.author.email,
        avatar: node.author.avatar
      },
      category: node.category as NodeCategory,
      tier: node.tier as NodeTier,
      price: node.price,
      subscriptionType: node.subscriptionType,
      tags: JSON.parse(node.tags || '[]'),
      dependencies: JSON.parse(node.dependencies || '[]'),
      permissions: JSON.parse(node.permissions || '[]'),
      icon: node.icon,
      screenshots: JSON.parse(node.screenshots || '[]'),
      downloadUrl: node.downloadUrl,
      repositoryUrl: node.repositoryUrl,
      documentationUrl: node.documentationUrl,
      verified: node.verified,
      featured: node.featured,
      rating: node.rating,
      reviewCount: node._count.reviews,
      downloads: node.downloads,
      weeklyDownloads: node.weeklyDownloads,
      lastUpdated: node.lastUpdated,
      createdAt: node.createdAt,
      compatibility: JSON.parse(node.compatibility || '{}'),
      changelog: node.changelog ? JSON.parse(node.changelog) : undefined
    }));

    return NextResponse.json({
      nodes: transformedNodes,
      total,
      page,
      totalPages: Math.ceil(total / limit)
    });

  } catch (error) {
    console.error('Error fetching nodes:', error);
    return NextResponse.json(
      { error: 'Failed to fetch nodes' },
      { status: 500 }
    );
  }
}

// POST /api/marketplace/nodes - Create a new node (for developers)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get user from database to get the ID
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const formData = await request.formData();

    // Extract node data from form
    const nodeData = {
      name: formData.get('name') as string,
      version: formData.get('version') as string,
      description: formData.get('description') as string,
      longDescription: formData.get('longDescription') as string,
      category: formData.get('category') as string,
      tier: formData.get('tier') as string,
      price: formData.get('price') ? parseFloat(formData.get('price') as string) : null,
      subscriptionType: formData.get('subscriptionType') as string,
      tags: formData.get('tags') as string, // JSON string
      dependencies: formData.get('dependencies') as string, // JSON string
      permissions: formData.get('permissions') as string, // JSON string
      icon: formData.get('icon') as string,
      screenshots: formData.get('screenshots') as string, // JSON string
      repositoryUrl: formData.get('repositoryUrl') as string,
      documentationUrl: formData.get('documentationUrl') as string,
      compatibility: formData.get('compatibility') as string, // JSON string
    };

    // Validate required fields
    if (!nodeData.name || !nodeData.version || !nodeData.description || !nodeData.category) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // TODO: Handle file uploads (node package, icon, screenshots)
    // For now, we'll use placeholder URLs
    const downloadUrl = `/api/marketplace/download/${nodeData.name}-${nodeData.version}.zip`;

    // Create node in database
    const node = await prisma.nodePlugin.create({
      data: {
        ...nodeData,
        authorId: user.id,
        downloadUrl,
        verified: false, // Needs manual verification
        featured: false,
        rating: 0,
        reviewCount: 0,
        downloads: 0,
        weeklyDownloads: 0,
        compatibility: nodeData.compatibility || '{}',
        tags: nodeData.tags || '[]',
        dependencies: nodeData.dependencies || '[]',
        permissions: nodeData.permissions || '[]',
        screenshots: nodeData.screenshots || '[]'
      },
      include: {
        author: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true
          }
        }
      }
    });

    // Transform response
    const transformedNode = {
      id: node.id,
      name: node.name,
      version: node.version,
      description: node.description,
      longDescription: node.longDescription,
      author: {
        name: node.author.name || 'Unknown',
        email: node.author.email,
        avatar: node.author.avatar
      },
      category: node.category as NodeCategory,
      tier: node.tier as NodeTier,
      price: node.price,
      subscriptionType: node.subscriptionType,
      tags: JSON.parse(node.tags || '[]'),
      dependencies: JSON.parse(node.dependencies || '[]'),
      permissions: JSON.parse(node.permissions || '[]'),
      icon: node.icon,
      screenshots: JSON.parse(node.screenshots || '[]'),
      downloadUrl: node.downloadUrl,
      repositoryUrl: node.repositoryUrl,
      documentationUrl: node.documentationUrl,
      verified: node.verified,
      featured: node.featured,
      rating: node.rating,
      reviewCount: 0,
      downloads: node.downloads,
      weeklyDownloads: node.weeklyDownloads,
      lastUpdated: node.lastUpdated,
      createdAt: node.createdAt,
      compatibility: JSON.parse(node.compatibility || '{}'),
      changelog: node.changelog ? JSON.parse(node.changelog) : undefined
    };

    return NextResponse.json(transformedNode, { status: 201 });

  } catch (error) {
    console.error('Error creating node:', error);
    return NextResponse.json(
      { error: 'Failed to create node' },
      { status: 500 }
    );
  }
}
